<template>
  <view class="counseling-page">

    <!-- 页面标题和筛选选项 -->
    <view class="page-header">
      <view class="header-title-container">
        <text class="header-title">心理咨询</text>
        <text class="header-subtitle">每一次倾诉都是一次疗愈之旅</text>
      </view>

      <view class="filter-container">
        <view class="filter-item">
          <picker
            :value="roleIndex"
            :range="roleOptions"
            @change="handleRoleChange"
            class="filter-picker"
          >
            <view class="picker-value">
              <text class="picker-icon user"></text>
              <text>{{ roleOptions[roleIndex] }}</text>
            </view>
          </picker>
        </view>

        <button class="refresh-button" @click="loadSessions">
          <image class="icon" src="/static/icons/refresh.svg" mode="aspectFit"></image>
          <text>刷新</text>
        </button>

        <button class="usage-button" @click="showUsagePopup = true">
          <image class="icon" src="/static/icons/info.svg" mode="aspectFit"></image>
          <text>说明</text>
        </button>
      </view>
    </view>

    <view class="content-container">
      <!-- 使用说明弹窗 -->
      <uni-popup ref="usagePopup" type="center" :mask-click="true">
        <view class="usage-popup-content">
          <view class="popup-title">功能使用说明</view>
          <view class="popup-description">
            <text>本心理咨询功能专为心理咨询师设计。</text>
            <text>在为来访者提供心理咨询服务时，咨询师可利用此功能进行会话分析，并最终生成包含咨询内容、潜在风险、专业建议及总结的详细咨询报告。</text>
            <text>旨在辅助咨询师更高效、系统地记录和评估咨询过程，提升咨询质量。</text>
            <text class="agreement-reminder">请咨询师务必遵守相关咨询服务协议及职业道德规范。</text>
          </view>
          <button class="popup-close-button" @click="showUsagePopup = false">我知道了</button>
        </view>
      </uni-popup>
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>

      <view v-else>
        <view class="sessions-list">
          <view
            v-for="session in sessions"
            :key="session.id"
            class="session-card"
            @click="viewSessionDetail(session.id)"
            @longpress="handleLongPress(session.id)"
          >
            <!-- 卡片顶部状态条 -->
            <view class="card-status-bar" :class="getStatusClass(session.status)"></view>

            <view class="session-header">
              <view class="session-title-row">
                <view class="session-title-id-group">
                  <text class="session-title">{{ session.title || '咨询会话' }}</text>
                  <text class="session-id">ID: {{ formatSessionId(session.session_id || session.id) }}</text>
                </view>
                <view class="session-status" :class="getStatusClass(session.status)">
                  {{ getStatusText(session.status) }}
                </view>
              </view>
            </view>

            <!-- 会话信息区域 -->
            <view class="session-info-container">
              <view class="session-info-row">
                <view class="info-col">
                  <view class="info-icon user"></view>
                  <view class="info-content">
                    <text class="info-label">来访者</text>
                    <text class="info-value">{{ session.client_name || '匿名' }}</text>
                  </view>
                </view>
                <view class="info-col">
                  <view class="info-icon chart"></view>
                  <view class="info-content">
                    <text class="info-label">性别/年龄</text>
                    <text class="info-value">{{ getGenderText(session.client_gender) }}/{{ session.client_age || '未知' }}</text>
                  </view>
                </view>
              </view>

              <view class="session-info-row">
                <view class="info-col">
                  <view class="info-icon time"></view>
                  <view class="info-content">
                    <text class="info-label">咨询时间</text>
                    <text class="info-value time-value">{{ formatDate(session.session_date || session.scheduled_time) }}</text>
                  </view>
                </view>
                <view class="info-col">
                  <!-- 修复咨询时长字段的图标显示 -->
                  <image class="info-icon" src="/static/icons/time-start.svg" mode="aspectFit"></image>
                  <view class="info-content">
                    <text class="info-label">咨询时长</text>
                    <text class="info-value">{{ getSessionDuration(session) }}</text>
                  </view>
                </view>
              </view>

              <view class="session-info-row" v-if="session.counselor || (session.analysis && session.analysis.risk_level)">
                <view class="info-col" v-if="session.counselor">
                  <view class="info-icon therapist"></view>
                  <view class="info-content">
                    <text class="info-label">咨询师</text>
                    <text class="info-value">{{ session.counselor.name || '未分配' }}</text>
                  </view>
                </view>
                <view class="info-col" v-if="session.analysis && session.analysis.risk_level">
                  <view class="info-icon risk"></view>
                  <view class="info-content">
                    <text class="info-label">风险等级</text>
                    <text class="info-value risk-level" :class="getRiskLevelClass(session.analysis.risk_level)">
                      {{ getRiskLevelText(session.analysis.risk_level) }}
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 描述和分析状态 -->
            <view class="session-footer" v-if="(session.description && session.description !== session.topic) || session.analysis">
              <view class="session-description" v-if="session.description && session.description !== session.topic">
                <text class="description-text">{{ session.description }}</text>
              </view>
              <!-- 显示分析状态（如果有） -->
              <view class="analysis-status" v-if="session.analysis && session.analysis.status">
                <text class="analysis-label">分析:</text>
                <text class="analysis-value" :class="getAnalysisStatusClass(session.analysis.status)">
                  {{ getAnalysisStatusText(session.analysis.status) }}
                </text>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="session-actions">
              <view class="action-buttons">
                <button
                  v-if="canViewReport(session)"
                  class="action-button report-button"
                  @click.stop="viewReport(session.id)"
                >
                  查看报告
                </button>
                <button
                  v-if="canViewReport(session)"
                  class="action-button share-button"
                  @click.stop="shareReport(session.id)"
                >
                  分享报告
                </button>
                <button
                  v-else-if="canAnalyze(session)"
                  class="action-button analyze-button"
                  @click.stop="startAnalysis(session.id)"
                >
                  开始分析
                </button>

                <button
                  v-if="canCancelSession(session)"
                  class="action-button cancel-button"
                  @click.stop="confirmCancelSession(session.id)"
                >
                  取消咨询
                </button>
                <button
                  v-if="canRateSession(session)"
                  class="action-button rate-button"
                  @click.stop="rateSession(session.id)"
                >
                  评价
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 悬浮按钮 -->
    <view class="floating-button" @click="startNewSession">
      <text class="floating-button-icon">+</text>
    </view>
  </view>
</template>

<script>
import counselingService from '../../services/counselingService';
import uniPopup from '../../components/uni-popup/uni-popup.vue'; // 引入 uni-popup 组件

export default {
  components: {
    uniPopup
  },
  data() {
    return {
      sessions: [],
      loading: true,
      roleOptions: ['全部', '咨询师', '来访者'],
      roleIndex: 0,
      roleValues: ['all', 'therapist', 'client'],
      showUsagePopup: false // 控制使用说明弹窗显示/隐藏
    }
  },
  onLoad() {
    this.loadSessions()
  },
  watch: {
    showUsagePopup(newVal) {
      if (newVal) {
        this.$refs.usagePopup.open();
      } else {
        this.$refs.usagePopup.close();
      }
    }
  },

  // 页面显示时刷新数据
  onShow() {
    // 如果页面已经加载过，则刷新数据
    if (this.sessions && this.sessions.length > 0) {
      console.log('页面重新显示，刷新数据');
      this.loadSessions();
    }
  },
  methods: {
    async loadSessions() {
      try {
        this.loading = true

        // 获取当前选择的角色
        const role = this.roleValues[this.roleIndex]
        console.log(`加载咨询会话列表: role=${role}`)

        // 调用咨询服务
        const result = await counselingService.getSessions(role)

        if (result.success) {
          console.log('获取咨询会话列表成功:', result.data)
          this.sessions = result.data || []

          // 调试：检查会话数据结构
          if (this.sessions.length > 0) {
            console.log('第一个会话数据结构:', JSON.stringify(this.sessions[0], null, 2))
            console.log('会话数量:', this.sessions.length)
            console.log('有analysis对象的会话数量:', this.sessions.filter(s => s.analysis).length)

            // 检查每个会话的analysis对象
            this.sessions.forEach((session, index) => {
              if (session.analysis) {
                console.log(`会话 ${index} (ID: ${session.id}) 的analysis对象:`,
                  JSON.stringify(session.analysis, null, 2))
              }
            })

            // 检查每个会话是否可以查看报告
            this.sessions.forEach((session, index) => {
              const canView = this.canViewReport(session)
              console.log(`会话 ${index} (ID: ${session.id}) 是否可以查看报告: ${canView}`)
            })
          }

          // 检查分析状态和客户信息
          this.sessions.forEach(session => {
            // 记录分析状态
            if (session.analysis) {
              console.log(`会话 ID: ${session.id}, 分析状态: ${session.analysis.status}`)

              // 确保风险等级存在
              if (session.analysis.status === 'completed' && !session.analysis.risk_level) {
                console.log(`会话 ID: ${session.id} 分析已完成但没有风险等级，设置默认风险等级: low`)
                session.analysis.risk_level = 'low'
              }
            }

            // 检查客户信息是否完整
            if (!session.client_name || !session.client_gender || !session.client_age) {
              console.warn(`会话 ID: ${session.id} 的客户信息不完整`)
            }
          })

          // 为已完成的会话获取分析状态
          await this.loadAnalysisForCompletedSessions()

          // 如果没有数据，不显示提示
          // 已移除"暂无咨询记录"的提示
        } else {
          console.error('获取咨询会话列表失败:', result.error)

          // 显示详细错误信息
          uni.showModal({
            title: '加载失败',
            content: result.error || '无法获取咨询记录',
            showCancel: false
          })

          // 设置为空数组
          this.sessions = []
        }
      } catch (error) {
        console.error('加载咨询记录失败:', error)

        // 显示详细错误信息
        uni.showModal({
          title: '加载失败',
          content: error.message || '无法获取咨询记录',
          showCancel: false
        })

        // 设置为空数组
        this.sessions = []
      } finally {
        this.loading = false
      }
    },

    // 为已完成的会话获取分析状态
    async loadAnalysisForCompletedSessions() {
      try {
        // 找出所有已完成的会话
        const completedSessions = this.sessions.filter(session =>
          session.status === 'completed' &&
          (!session.analysis || !session.analysis.risk_level || !session.analysis.status)
        );

        if (completedSessions.length === 0) {
          console.log('没有需要获取分析状态的已完成会话');
          return;
        }

        console.log(`开始获取 ${completedSessions.length} 个已完成会话的分析状态`);

        // 为每个已完成的会话获取分析状态
        const promises = completedSessions.map(async (session) => {
          try {
            const analysisResult = await counselingService.getSessionAnalysis(session.id);

            if (analysisResult && !analysisResult.error) {
              console.log(`成功获取会话 ID: ${session.id} 的分析状态:`, analysisResult.status);

              // 更新会话的分析状态
              const index = this.sessions.findIndex(s => s.id === session.id);
              if (index !== -1) {
                // 确保会话有analysis对象
                if (!this.sessions[index].analysis) {
                  this.sessions[index].analysis = {};
                }

                // 更新分析状态
                if (analysisResult.status) {
                  this.sessions[index].analysis.status = analysisResult.status;
                }

                // 更新风险等级
                if (analysisResult.risk_level) {
                  this.sessions[index].analysis.risk_level = analysisResult.risk_level;
                } else if (analysisResult.status === 'completed' && !this.sessions[index].analysis.risk_level) {
                  this.sessions[index].analysis.risk_level = 'low';
                }

                // 更新报告内容标志
                if (analysisResult.report_content) {
                  this.sessions[index].analysis.has_report = true;
                }
              }
            }
          } catch (error) {
            console.error(`获取会话 ID: ${session.id} 的分析状态失败:`, error);
          }
        });

        // 等待所有请求完成
        await Promise.all(promises);

        // 强制更新视图
        this.$forceUpdate();

        console.log('所有分析状态获取完成');
      } catch (error) {
        console.error('获取分析状态失败:', error);
      }
    },

    // 处理角色选择变化
    handleRoleChange(e) {
      this.roleIndex = e.detail.value
      this.loadSessions()
    },

    formatDate(date) {
      if (!date) return '未设置'

      const d = new Date(date)
      const year = d.getFullYear()
      const month = (d.getMonth() + 1).toString().padStart(2, '0')
      const day = d.getDate().toString().padStart(2, '0')
      const hours = d.getHours().toString().padStart(2, '0')
      const minutes = d.getMinutes().toString().padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    getStatusText(status) {
      const statusMap = {
        'scheduled': '已创建',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      }

      // 如果状态不在预定义的映射中，返回空字符串
      return statusMap[status] || ''
    },

    getStatusClass(status) {
      const classMap = {
        'scheduled': 'status-scheduled',
        'in_progress': 'status-in-progress',
        'completed': 'status-completed',
        'cancelled': 'status-cancelled'
      }

      return classMap[status] || ''
    },

    getSessionTypeText(type) {
      const typeMap = {
        'video': '视频咨询',
        'voice': '语音咨询',
        'text': '文字咨询',
        'in_person': '面对面咨询'
      }

      return typeMap[type] || '未知方式'
    },

    getAnalysisStatusText(status) {
      if (status === null || status === undefined) return ''; // Handle null/undefined as empty string
      const map = { 
        'pending': '等待分析', 
        'processing': '分析中', 
        'completed': '分析完成', 
        'failed': '分析失败', 
        'user_cancelled': '已取消' 
      };
      
      if (map[status]) return map[status];

      if (typeof status === 'string') {
        if (status.includes('fail') || status.includes('error')) return '分析失败';
        if (status.includes('process') || status.includes('running')) return '分析中';
        if (status.includes('complete') || status.includes('done') || status.includes('success')) return '分析完成';
        if (status.includes('wait') || status.includes('pending')) return '等待分析';
      }
      return ''; // Default fallback to empty string
    },

    getAnalysisStatusClass(status) {
      const classMap = {
        'not_started': 'status-not-started',
        'pending': 'status-pending',
        'processing': 'status-processing',
        'completed': 'status-completed',
        'failed': 'status-failed'
      }

      return classMap[status] || ''
    },

    // 获取性别文本
    getGenderText(gender) {
      if (!gender) return '未知'

      const genderMap = {
        'male': '男',
        'female': '女',
        'other': '其他'
      }

      return genderMap[gender.toLowerCase()] || gender
    },

    // 获取风险等级文本
    getRiskLevelText(level) {
      if (!level) return '未评估'

      // 将风险等级转换为小写进行比较
      const normalizedLevel = typeof level === 'string' ? level.toLowerCase() : '';

      const levelMap = {
        'low': '低风险',
        'medium': '中等风险',
        'high': '高风险',
        'critical': '严重风险'
      }

      return levelMap[normalizedLevel] || level
    },

    // 获取风险等级样式类
    getRiskLevelClass(level) {
      if (!level) return 'risk-low' // 默认使用低风险样式

      // 将风险等级转换为小写进行比较
      const normalizedLevel = typeof level === 'string' ? level.toLowerCase() : '';

      const classMap = {
        'low': 'risk-low',
        'medium': 'risk-medium',
        'high': 'risk-high',
        'critical': 'risk-critical'
      }

      return classMap[normalizedLevel] || 'risk-low'
    },

    // 格式化会话编号，使其显示为8位数字
    formatSessionId(id) {
      if (!id) return '';

      // 如果是cs_前缀的UUID格式，转换为8位数字
      if (typeof id === 'string' && id.startsWith('cs_')) {
        // 取UUID的前8位十六进制，转换为数字
        const hexPart = id.substring(3, 11);
        // 将十六进制转换为十进制数字
        const numericId = parseInt(hexPart, 16);
        // 确保是8位数字，不足前面补0
        return numericId.toString().padStart(8, '0');
      }

      // 如果是普通UUID格式，只取前8位
      if (typeof id === 'string' && id.includes('-')) {
        return id.split('-')[0];
      }

      // 如果是数字，确保是8位，不足前面补0
      if (typeof id === 'number' || !isNaN(parseInt(id))) {
        return parseInt(id).toString().padStart(8, '0');
      }

      // 其他情况直接返回
      return id.toString();
    },

    // 获取会话时长
    getSessionDuration(session) {
      // 如果有录音且有时长，优先显示录音时长
      if (session.recordings && Array.isArray(session.recordings) &&
          session.recordings.length > 0 && session.recordings[0].duration_seconds) {

        const totalSeconds = session.recordings[0].duration_seconds;
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = Math.floor(totalSeconds % 60);

        return `${minutes}:${seconds.toString().padStart(2, '0')} (实际时长)`;
      }

      // 否则显示预计时长
      return session.duration_minutes ? `${session.duration_minutes} 分钟` : '未设置';
    },

    canViewReport(session) {
      if (!session) {
        console.log('canViewReport: session 为空');
        return false;
      }

      console.log(`canViewReport 检查会话 ID: ${session.id}, 会话对象:`, JSON.stringify(session, null, 2));

      // 确保session.analysis存在
      if (!session.analysis) {
        console.log(`会话 ID: ${session.id} 没有analysis对象`);
        return false;
      }

      // 如果分析状态为 not_started 或 pending 或 processing，则不能查看报告
      if (['not_started', 'pending', 'processing'].includes(session.analysis.status)) {
        console.log(`会话 ID: ${session.id} 分析状态为 ${session.analysis.status}，不能查看报告`);
        return false;
      }

      // 只有分析状态为 completed 且有报告内容时才能查看报告
      const hasCompletedStatus = session.analysis.status === 'completed';
      const hasReportContent = !!session.analysis.report_content;

      console.log('检查是否可以查看报告:', {
        'sessionId': session.id,
        'hasCompletedStatus': hasCompletedStatus,
        'hasReportContent': hasReportContent,
        'analysisStatus': session.analysis.status || 'none',
        'hasAnalysisObject': true,
        'analysis对象': JSON.stringify(session.analysis, null, 2)
      });

      // 确保分析完成后显示风险等级
      if (hasCompletedStatus && !session.analysis.risk_level) {
        console.log(`会话 ID: ${session.id} 分析已完成但没有风险等级，设置默认风险等级: low`);
        session.analysis.risk_level = 'low';
      }

      // 必须同时满足状态为completed且有报告内容
      const result = hasCompletedStatus && hasReportContent;
      console.log(`会话 ID: ${session.id} canViewReport 结果: ${result}`);
      return result;
    },

    canAnalyze(session) {
      return session.status === 'completed' &&
             (!session.analysis || session.analysis.status === 'failed' || session.analysis.status === 'pending')
    },

    canJoinSession(session) {
      // 只要会话不是已取消状态，都可以开始对话
      return session.status !== 'cancelled'
    },

    canCancelSession(session) {
      return session.status === 'scheduled'
    },

    canRateSession(session) {
      return session.status === 'completed'
    },

    startNewSession() {
      uni.navigateTo({
        url: '/pages/counseling/create'
      })
    },

    viewSessionDetail(sessionId) {
      uni.navigateTo({
        url: `/pages/counseling/detail?id=${sessionId}`
      })
    },

    joinSession(sessionId) {
      uni.navigateTo({
        url: `/pages/counseling/detail?id=${sessionId}&action=join`
      })
    },

    confirmCancelSession(sessionId) {
      uni.showModal({
        title: '取消咨询',
        content: '确定要取消这个咨询吗？',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.cancelSession(sessionId)
          }
        }
      })
    },

    async cancelSession(sessionId) {
      try {
        uni.showLoading({
          title: '取消中...'
        })

        const result = await counselingService.updateSession(sessionId, {
          status: 'cancelled'
        })

        uni.hideLoading()

        if (result.success) {
          uni.showToast({
            title: '已取消咨询',
            icon: 'success'
          })

          // 更新状态
          const index = this.sessions.findIndex(s => s.id === sessionId)
          if (index !== -1) {
            this.sessions[index].status = 'cancelled'
          }
        } else {
          uni.showModal({
            title: '取消失败',
            content: result.error || '取消咨询失败，请稍后重试',
            showCancel: false
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('取消咨询失败:', error)
        uni.showModal({
          title: '取消失败',
          content: '取消咨询失败，请稍后重试',
          showCancel: false
        })
      }
    },

    rateSession(sessionId) {
      uni.navigateTo({
        url: `/pages/counseling/detail?id=${sessionId}&action=rate`
      })
    },

    viewReport(sessionId) {
      uni.navigateTo({
        url: `/pages/counseling/report?id=${sessionId}`
      })
    },

    async shareReport(sessionId) {
      try {
        // 检查sessionId是否有效
        if (!sessionId || isNaN(sessionId)) {
          uni.showToast({
            title: '无效的会话ID，无法分享报告',
            icon: 'none'
          });
          console.error('无效的会话ID:', sessionId);
          return;
        }

        uni.showLoading({
          title: '准备分享...',
          mask: true
        });

        // 确保sessionId是有效的数字
        const validSessionId = parseInt(sessionId);
        console.log(`开始准备分享报告，会话ID: ${validSessionId}`);

        try {
          // 先下载报告
          const result = await counselingService.downloadReportAsDocx(validSessionId);

          if (result.statusCode !== 200) {
            let errorMsg = '准备分享失败';
            if (result.statusCode === 404) {
              errorMsg = '报告不存在或尚未生成';
            } else if (result.statusCode === 403) {
              errorMsg = '没有权限访问该报告';
            } else if (result.statusCode === 422) {
              errorMsg = '无效的请求参数';
            }

            uni.hideLoading();
            uni.showToast({
              title: errorMsg,
              icon: 'none'
            });
            return;
          }

          // 生成文件名 (在APP-PLUS和H5环境中使用)
          // const reportFileName = `心理咨询报告_${sessionId}_${new Date().toISOString().split('T')[0]}.docx`;

          // #ifdef APP-PLUS
          // 立即隐藏加载提示，避免分享界面出现时仍显示加载
          uni.hideLoading();

          // 直接使用系统分享，不通过Promise
          console.log('使用系统分享功能');
          try {
            // 使用plus.share直接分享，不等待Promise
            plus.share.sendWithSystem({
              type: 'file',
              title: '心理咨询报告',
              href: result.tempFilePath,
              content: '我的心理咨询报告'
            });
          } catch (error) {
            console.error('系统分享失败:', error);
            uni.showToast({
              title: '分享失败',
              icon: 'none'
            });
          }
          // #endif

          // #ifdef H5 || MP
          uni.hideLoading();
          // 在H5环境中，直接触发浏览器下载
          uni.showModal({
            title: '分享提示',
            content: '当前平台不支持直接分享文件，正在准备下载报告',
            showCancel: false,
            success: () => {
              // 如果在H5环境中，可以尝试直接下载
              if (uni.getSystemInfoSync().platform === 'web') {
                // 从counselingService中获取API基础URL
                const apiBaseUrl = counselingService.getApiBaseUrl();
                window.open(`${apiBaseUrl}/reports/counseling/${validSessionId}/export/docx`, '_blank');
              }
            }
          });
          // #endif
        } catch (error) {
          uni.hideLoading();
          console.error('分享准备失败:', error);

          // 如果是因为未安装微信导致的错误，提示用户
          if (error.message && error.message.includes('未检测到微信安装')) {
            uni.showModal({
              title: '分享提示',
              content: '未检测到微信安装，将使用系统分享',
              showCancel: false,
              success: () => {
                // #ifdef APP-PLUS
                // 尝试使用系统分享
                try {
                  plus.share.sendWithSystem({
                    type: 'file',
                    title: '心理咨询报告',
                    href: result.tempFilePath, // 使用之前下载的文件路径
                    content: '我的心理咨询报告'
                  });
                } catch (err) {
                  console.error('系统分享失败:', err);
                  uni.showToast({
                    title: '分享失败',
                    icon: 'none'
                  });
                }
                // #endif
              }
            });
          } else {
            // 其他错误
            uni.showToast({
              title: '分享失败',
              icon: 'none'
            });
          }
        }
      } catch (error) {
        uni.hideLoading();
        console.error('分享报告失败:', error);
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        });
      }
    },

    async startAnalysis(sessionId) {
      try {
        uni.showLoading({
          title: '开始分析...'
        })

        const result = await counselingService.analyzeDialogue(sessionId, {}, false, false)

        uni.hideLoading()

        if (result.success) {
          uni.showToast({
            title: '分析已开始',
            icon: 'success'
          })

          // 更新会话状态
          const index = this.sessions.findIndex(s => s.id === sessionId)
          if (index !== -1) {
            if (!this.sessions[index].analysis) {
              this.sessions[index].analysis = {}
            }
            this.sessions[index].analysis.status = 'processing'

            // 5秒后获取最新的分析状态
            setTimeout(async () => {
              try {
                const analysisResult = await counselingService.getSessionAnalysis(sessionId);

                if (analysisResult && !analysisResult.error) {
                  console.log(`5秒后检查分析状态: ${analysisResult.status}`);

                  // 更新会话的分析状态
                  const updatedIndex = this.sessions.findIndex(s => s.id === sessionId);
                  if (updatedIndex !== -1) {
                    if (!this.sessions[updatedIndex].analysis) {
                      this.sessions[updatedIndex].analysis = {};
                    }

                    // 更新分析状态
                    if (analysisResult.status) {
                      this.sessions[updatedIndex].analysis.status = analysisResult.status;
                    }

                    // 强制更新视图
                    this.$forceUpdate();
                  }
                }
              } catch (error) {
                console.error('获取最新分析状态失败:', error);
              }
            }, 5000);
          }
        } else {
          uni.showToast({
            title: result.error || '开始分析失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('开始分析失败:', error)
        uni.showToast({
          title: '开始分析失败',
          icon: 'none'
        })
      }
    },

    handleLongPress(sessionId) {
      // 震动反馈
      uni.vibrateShort({
        success: function() {
          console.log('震动成功');
        }
      });

      // 显示操作菜单
      uni.showActionSheet({
        itemList: ['删除咨询'],
        itemColor: '#f5222d',
        success: (res) => {
          if (res.tapIndex === 0) {
            // 用户点击了"删除咨询"
            this.confirmDeleteSession(sessionId);
          }
        }
      });
    },

    confirmDeleteSession(sessionId) {
      uni.showModal({
        title: '删除会话',
        content: '确定要删除这个咨询会话吗？删除后无法恢复。',
        confirmText: '删除',
        confirmColor: '#f5222d',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.deleteSession(sessionId)
          }
        }
      })
    },

    async deleteSession(sessionId) {
      try {
        uni.showLoading({
          title: '删除中...'
        })

        const result = await counselingService.deleteSession(sessionId)

        uni.hideLoading()

        if (result.success) {
          uni.showToast({
            title: '已删除会话',
            icon: 'success'
          })

          // 从列表中移除该会话
          const index = this.sessions.findIndex(s => s.id === sessionId)
          if (index !== -1) {
            this.sessions.splice(index, 1)
          }
        } else {
          uni.showModal({
            title: '删除失败',
            content: result.error || '删除会话失败，请稍后重试',
            showCancel: false
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('删除会话失败:', error)
        uni.showModal({
          title: '删除失败',
          content: '删除会话失败，请稍后重试',
          showCancel: false
        })
      }
    }
  }
}
</script>

<style scoped>
.counseling-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f5f0; /* 温暖的淡米色/象牙白背景 */
  color: #4a4a4a; /* 深灰色文字，不那么刺眼 */
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 页面标题和筛选选项 */
.page-header {
  padding: 16px 16px 10px;
  margin-top: 0;
  background-color: white;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 1;
}

.header-title-container {
  margin-bottom: 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  color: #8a63a9;
  margin-bottom: 4px;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.05);
  display: block;
}

.header-subtitle {
  font-size: 14px;
  color: #5a5a5a;
  font-weight: 500;
  line-height: 1.5;
  display: block;
  margin-bottom: 10px; /* 调整副标题和筛选区域的间距 */
}

.filter-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
  gap: 12px;
}

.usage-button {
  background-color: rgba(169, 139, 199, 0.08); /* 淡紫色背景 */
  border: none;
  font-size: 13px; /* 减小字体大小 */
  color: #a98bc7; /* 温暖的紫色 */
  display: flex;
  align-items: center;
  padding: 4px 10px; /* 减小内边距 */
  border-radius: 14px; /* 调整圆角 */
  height: auto; /* 允许高度自适应 */
  line-height: normal; /* 恢复默认行高 */
}

.usage-button .icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

/* 使用说明弹窗样式 */
.usage-popup-content {
  background-color: white;
  border-radius: 16px;
  padding: 25px;
  width: calc(100% - 60px); /* 弹窗宽度，减去左右各30px的边距 */
  max-width: 400px; /* 最大宽度 */
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03); /* 再次减弱阴影效果，使其几乎不可见 */
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box; /* 确保padding和border包含在width内 */
  margin: 0 auto; /* 确保内容在弹窗内部居中 */
}

.popup-title {
  font-size: 18px;
  font-weight: 700;
  color: #6a5acd; /* 较深的紫色 */
  margin-bottom: 15px;
  text-align: center;
}

.popup-description {
  font-size: 14px;
  color: #555;
  line-height: 1.6;
  text-align: left;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px; /* 段落间距 */
}

.popup-description text {
  display: block; /* 确保每段文字独立一行 */
}

.agreement-reminder {
  font-size: 12px; /* 提示文字小一点 */
  color: #888; /* 颜色更柔和 */
  margin-top: 10px; /* 与上方内容保持距离 */
  font-style: italic; /* 斜体 */
}

.popup-close-button {
  background: linear-gradient(135deg, #b69fdb, #e0b0c9); /* 温暖的紫色到粉色渐变 */
  color: white;
  border: none;
  border-radius: 25px;
  padding: 10px 25px;
  font-size: 15px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(169, 139, 199, 0.3);
  transition: all 0.3s ease;
  width: 80%;
  max-width: 200px;
}

.popup-close-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(169, 139, 199, 0.2);
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-picker {
  min-width: 100px;
}

.picker-value {
  display: flex;
  align-items: center;
  font-size: 13px; /* 减小字体大小 */
  color: #a98bc7; /* 温暖的紫色 */
  padding: 4px 10px; /* 减小内边距 */
  background-color: rgba(169, 139, 199, 0.08); /* 淡紫色背景 */
  border-radius: 14px; /* 调整圆角 */
}

.picker-icon::before {
  content: url('../../static/icons/user.svg'); /* 替换用户图标 */
  display: inline-block;
  width: 20px;
  height: 20px;
  vertical-align: middle;
}

.refresh-button {
  background-color: rgba(169, 139, 199, 0.08); /* 淡紫色背景 */
  border: none;
  font-size: 13px; /* 减小字体大小 */
  color: #a98bc7; /* 温暖的紫色 */
  display: flex;
  align-items: center;
  padding: 4px 10px; /* 减小内边距 */
  border-radius: 14px; /* 调整圆角 */
  height: auto; /* 允许高度自适应 */
  line-height: normal; /* 恢复默认行高 */
}

.refresh-button .icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.content-container {
  flex: 1;
  padding: 12px; /* 减小内容区域的内边距 */
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(169, 139, 199, 0.2);
  border-top-color: #a98bc7; /* 温暖的紫色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #a98bc7; /* 温暖的紫色 */
  margin-bottom: 12px;
}

.empty-description {
  font-size: 15px;
  color: #4a4a4a; /* 深灰色文字 */
  margin-bottom: 32px;
  line-height: 1.6;
  max-width: 280px;
}

.primary-button {
  background: linear-gradient(135deg, #b69fdb, #e0b0c9); /* 温暖的紫色到粉色渐变 */
  color: white;
  border: none;
  border-radius: 30px;
  padding: 14px 32px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 8px 20px rgba(169, 139, 199, 0.3);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.primary-button:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.primary-button:hover:after {
  opacity: 1;
}

.primary-button:active {
  transform: translateY(3px);
  box-shadow: 0 4px 10px rgba(169, 139, 199, 0.2);
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 8px; /* 进一步减小会话卡片之间的间距 */
}

.session-card {
  background-color: rgba(255, 255, 255, 0.9); /* 半透明白色背景 */
  backdrop-filter: blur(10px); /* 磨砂玻璃效果 */
  -webkit-backdrop-filter: blur(10px); /* Safari 支持 */
  border-radius: 8px; /* 增加圆角 */
  padding: 0;
  box-shadow: 0 6px 20px rgba(169, 139, 199, 0.1), 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  overflow: hidden;
  position: relative;
  margin-bottom: 15px; /* 减小会话框之间的间隙 */
}

.session-card:active {
  transform: scale(0.98);
  box-shadow: 0 6px 16px rgba(169, 139, 199, 0.08);
}

/* 卡片顶部状态条 */
.card-status-bar {
  height: 6px;
  width: 100%;
}

.status-scheduled .card-status-bar {
  background: linear-gradient(90deg, #7a9fc8, #a98bc7); /* 温暖的蓝色到紫色 */
}

.status-in-progress .card-status-bar {
  background: linear-gradient(90deg, #7fb9a2, #7a9fc8); /* 温暖的绿松石到蓝色 */
}

.status-completed .card-status-bar {
  background: linear-gradient(90deg, #b69fdb, #e0b0c9); /* 温暖的紫色到粉色 */
}

.status-cancelled .card-status-bar {
  background: linear-gradient(90deg, #e6a29a, #e6c29a); /* 温暖的红色到橙色 */
}

.session-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
}

.session-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.session-title-id-group {
  display: flex;
  align-items: baseline; /* 确保标题和ID底部对齐 */
  flex: 1;
  min-width: 0; /* 允许内容收缩 */
}

.session-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px; /* 标题和ID之间的间距 */
}

.session-id {
  font-size: 12px;
  color: #999;
  font-family: monospace;
  white-space: nowrap; /* 确保ID不换行 */
}

.session-status {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-left: 8px;
  white-space: nowrap;
  font-weight: 500;
}

.status-scheduled {
  background-color: rgba(122, 159, 200, 0.15);
  color: #7a9fc8; /* 温暖的蓝色 */
}

.status-in-progress {
  background-color: rgba(127, 185, 162, 0.15);
  color: #7fb9a2; /* 温暖的绿松石 */
}

.status-completed {
  background-color: rgba(182, 159, 219, 0.15);
  color: #b69fdb; /* 温暖的紫色 */
}

.status-cancelled {
  background-color: rgba(230, 162, 154, 0.15);
  color: #e6a29a; /* 温暖的红色 */
}

/* 会话信息区域 */
.session-info-container {
  padding: 12px 16px;
  background-color: white;
}

.session-info-row {
  display: flex;
  margin-bottom: 12px;
}

.session-info-row:last-child {
  margin-bottom: 0;
}

.info-col {
  display: flex;
  flex: 1;
  align-items: flex-start;
  margin-right: 12px;
}

.info-col:last-child {
  margin-right: 0;
}

.info-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 6px;
  margin-top: 0;
  vertical-align: middle;
}

.info-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.info-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.time-value {
  font-size: 13px;
}

.risk-level {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

.risk-low {
  background-color: rgba(127, 185, 162, 0.15);
  color: #7fb9a2; /* 温暖的绿松石 */
  border: 1px solid rgba(127, 185, 162, 0.3);
}

.risk-medium {
  background-color: rgba(230, 194, 154, 0.15);
  color: #e6c29a; /* 温暖的黄色 */
  border: 1px solid rgba(230, 194, 154, 0.3);
}

.risk-high {
  background-color: rgba(230, 178, 154, 0.15);
  color: #e6b29a; /* 温暖的橙色 */
  border: 1px solid rgba(230, 178, 154, 0.3);
}

.risk-critical {
  background-color: rgba(230, 162, 154, 0.15);
  color: #e6a29a; /* 温暖的红色 */
  border: 1px solid rgba(230, 162, 154, 0.3);
}

/* 描述和分析状态 */
.session-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: #f9f9fb;
  border-top: 1px solid #f5f5f5;
  border-bottom: 1px solid #f5f5f5;
}

.session-description {
  flex: 1;
  margin-right: 8px;
}

.description-text {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-style: italic;
}

.analysis-status {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.analysis-label {
  font-size: 12px;
  color: #999;
  margin-right: 4px;
}

.analysis-value {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.status-pending {
  background-color: rgba(169, 139, 199, 0.08);
  color: #a98bc7; /* 温暖的紫色 */
}

.status-processing {
  background-color: rgba(122, 159, 200, 0.15);
  color: #7a9fc8; /* 温暖的蓝色 */
}

.status-completed {
  background-color: rgba(127, 185, 162, 0.15);
  color: #7fb9a2; /* 温暖的绿松石 */
}

.status-failed {
  background-color: rgba(230, 162, 154, 0.15);
  color: #e6a29a; /* 温暖的红色 */
}

/* 操作按钮 */
.session-actions {
  padding: 12px 16px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-button {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 13px;
  border: none;
  white-space: nowrap;
  height: 32px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-button {
  background: linear-gradient(135deg, #7fb9a2, #7a9fc8); /* 温暖的绿松石到蓝色 */
  color: white;
}

.share-button {
  background: linear-gradient(135deg, #7a9fc8, #a98bc7); /* 温暖的蓝色到紫色 */
  color: white;
}

.analyze-button {
  background: linear-gradient(135deg, #7a9fc8, #a98bc7); /* 温暖的蓝色到紫色 */
  color: white;
}

.cancel-button {
  background-color: #f3f4f6; /* 浅灰色 */
  color: #6b7280; /* 中灰色文字 */
  border: 1px solid rgba(203, 213, 225, 0.5);
}

.rate-button {
  background: linear-gradient(135deg, #e6c29a, #e6a29a); /* 温暖的黄色到橙色 */
  color: white;
}

/* 悬浮按钮 */
.floating-button {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: linear-gradient(135deg, #b69fdb, #e0b0c9); /* 温暖的紫色到粉色渐变 */
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 20px rgba(169, 139, 199, 0.4);
  z-index: 10;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.floating-button:active {
  transform: translateY(3px);
  box-shadow: 0 4px 10px rgba(169, 139, 199, 0.3);
}

.floating-button-icon {
  font-size: 32px;
  font-weight: 300;
}
  /* 图标样式 */
  .info-icon.user::before {
    content: url('../../static/icons/user.svg');
  }
  
  .info-icon.chart::before {
    content: url('../../static/icons/chart-filled.svg');
  }
  
  .info-icon.time::before {
    content: url('../../static/icons/calendar.svg');
  }
  
  .info-icon.duration::before {
    content: url('../../static/icons/clock.svg');
  }
  
  .info-icon.therapist::before {
    content: url('../../static/icons/headphones-filled.svg');
  }
  
  .info-icon.risk::before {
    content: url('../../static/icons/alert-triangle.svg');
  }
</style>